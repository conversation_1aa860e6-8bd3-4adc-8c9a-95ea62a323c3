# Wedding Guest Dashboard 🎊

A modern, React-based wedding guest management system with real-time updates and interactive visualizations.

## 🌟 Key Features

- **Real-time Guest Management**
  - Instant updates across all connected devices
  - Multi-user collaboration support
  - Automated password generation for guest access

- **Interactive Analytics**
  - Guest distribution visualization
  - Title-based breakdown (Mr/Mrs/Dr)
  - Approval status tracking

- **Advanced Filtering**
  - Search by name, email, or affiliation
  - Filter by approval status
  - Sort by various parameters

- **Export Capabilities**
  - CSV export functionality
  - Customizable data fields

## 🛠️ Tech Stack

- React (v19.0.0)
- Firebase Firestore
- Chart.js
- Custom CSS with wedding theming

## 🚀 Quick Start

1. Clone the repository:
```bash
git clone https://github.com/oni1997/wedding-admin-dashboard.git
```

2. Install dependencies:
```bash
npm install
```

3. Configure Firebase:
   - Create `.env` file with Firebase credentials
   - Enable Firestore in Firebase Console

4. Start development server:
```bash
npm start
```

## 📱 Features Breakdown

### Guest Management
- Add/Edit/Delete guests
- Automatic password generation
- Real-time status updates

### Analytics Dashboard
- Three visualization modes:
  - Bride/Groom distribution
  - Title breakdown
  - Approval status

### Admin Controls
- User role management
- Access control
- Data export options

## 🎨 Customization

Easily customize:
- Color schemes
- Fonts
- Layout
- Data fields

## 📄 License

MIT License - See LICENSE file for details

## 👤 Contact

Onesmus Maenzanise - <EMAIL>

Project Link: [https://github.com/oni1997/wedding-admin-dashboard](https://github.com/oni1997/wedding-admin-dashboard)
