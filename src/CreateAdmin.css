.create-admin-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 1rem;
  background-color: #f5f5f5;
  font-family: Arial, sans-serif;
}

.create-admin-form-container {
  background-color: white;
  padding: 2rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
}

.create-admin-form-container h1 {
  text-align: center;
  color: #6D8B74;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.create-admin-form {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-group {
  margin-bottom: 1.25rem;
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.form-group input {
  width: 100%;
  padding: 0.85rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px; /* Prevents zoom on iOS */
  box-sizing: border-box;
  -webkit-appearance: none; /* Removes default styling on iOS */
}

.create-admin-button {
  background-color: #6D8B74;
  color: white;
  border: none;
  padding: 0.95rem;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  margin-top: 1.25rem;
  transition: background-color 0.3s;
  width: 100%;
}

.create-admin-button:hover {
  background-color: #5A7561;
}

.create-admin-button:disabled {
  background-color: #a0b5a4;
  cursor: not-allowed;
}

.error-message {
  color: #d32f2f;
  margin-top: 0.75rem;
  text-align: center;
  font-size: 0.9rem;
}

.success-message {
  color: #2e7d32;
  background-color: #e8f5e9;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 0.95rem;
}

.admin-restricted {
  text-align: center;
  background-color: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  margin: 1.5rem auto;
  width: 90%;
}

.admin-restricted h2 {
  color: #d32f2f;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

/* Mobile optimizations */
@media (max-width: 480px) {
  .create-admin-form-container {
    padding: 1.5rem 1rem;
  }
  
  .create-admin-form-container h1 {
    font-size: 1.3rem;
  }
  
  .form-group label {
    font-size: 0.95rem;
  }
  
  .create-admin-button {
    padding: 0.85rem;
  }
}