import React, { useState } from 'react';
import { db } from './firebase';
import { collection, query, where, getDocs, updateDoc, doc } from 'firebase/firestore';
import './Login.css';
import { getDoc } from 'firebase/firestore';

const Login = ({ onLogin }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // First try to find the user in the users collection
      const usersRef = collection(db, "users");
      const q = query(usersRef, where("email", "==", email));
      const querySnapshot = await getDocs(q);

      // If not found in users collection, fall back to legacy user document
      if (querySnapshot.empty) {
        // Try the legacy user document
        const userRef = doc(db, "user", "user");
        const userDoc = await getDoc(userRef);

        if (!userDoc.exists()) {
          setError('User not found');
          setLoading(false);
          return;
        }

        const userData = userDoc.data();

        // Check credentials for legacy user
        if (userData.email === email && userData.password === password) {
          // Update last login time
          const currentTime = new Date().toISOString();
          
          // Pass user data to parent component
          onLogin({
            displayName: userData.displayName,
            email: userData.email,
            role: userData.role,
            lastLogin: currentTime
          });
        } else {
          setError('Invalid email or password');
        }
      } else {
        // User found in users collection
        const userDoc = querySnapshot.docs[0];
        const userData = userDoc.data();
        
        // Check password
        if (userData.password === password) {
          // Update last login time
          const currentTime = new Date().toISOString();
          
          // Update the lastLogin field in Firestore
          await updateDoc(userDoc.ref, {
            lastLogin: currentTime
          });
          
          // Pass user data to parent component
          onLogin({
            displayName: userData.displayName,
            email: userData.email,
            role: userData.role,
            lastLogin: currentTime
          });
        } else {
          setError('Invalid email or password');
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('An error occurred during login');
    }

    setLoading(false);
  };

  return (
    <div className="login-container">
      <div className="login-form-container">
        <h1>Wedding Dashboard Login</h1>
        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          
          {error && <div className="error-message">{error}</div>}
          
          <button type="submit" className="login-button" disabled={loading}>
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default Login;
