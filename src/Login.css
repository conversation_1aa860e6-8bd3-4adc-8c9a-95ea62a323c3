.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: Arial, sans-serif;
  padding: 1rem;
  box-sizing: border-box;
}

.login-form-container {
  background-color: white;
  padding: 2rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 380px;
  margin: 0 auto;
}

.login-form-container h1 {
  text-align: center;
  color: #6D8B74;
  margin-bottom: 1.5rem;
  font-size: 1.6rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-group {
  margin-bottom: 1.25rem;
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.form-group input {
  width: 100%;
  padding: 0.85rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px; /* Prevents zoom on iOS */
  box-sizing: border-box;
  -webkit-appearance: none; /* Removes default styling on iOS */
}

.login-button {
  background-color: #6D8B74;
  color: white;
  border: none;
  padding: 0.95rem;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  margin-top: 1.25rem;
  transition: background-color 0.3s;
  width: 100%;
  min-height: 48px; /* Better touch target */
}

.login-button:hover {
  background-color: #5A7561;
}

.login-button:disabled {
  background-color: #a0b5a4;
  cursor: not-allowed;
}

.error-message {
  color: #d32f2f;
  margin-top: 0.75rem;
  text-align: center;
  font-size: 0.9rem;
}

/* Mobile optimizations */
@media (max-width: 480px) {
  .login-form-container {
    padding: 1.5rem 1rem;
  }
  
  .login-form-container h1 {
    font-size: 1.4rem;
  }
  
  .form-group label {
    font-size: 0.95rem;
  }
  
  .login-button {
    padding: 0.85rem;
  }
}

/* Fix for iOS positioning issues */
@supports (-webkit-touch-callout: none) {
  .login-container {
    min-height: -webkit-fill-available;
  }
}