@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;600&family=Great+Vibes&display=swap');

body {
  font-family: 'Cormorant Garamond', serif;
  background-color: #f4f7f5;
  color: #3a3a3a;
  padding: 1rem;
  margin: 0;
}

.container {
  max-width: 1000px;
  margin: auto;
  background: white;
  padding: 1.5rem 1rem;
  border-radius: 10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: calc(100% - 2rem);
  box-sizing: border-box;
}

h1 {
  font-family: 'Great Vibes', cursive;
  font-size: 2.2rem;
  color: #6D8B74;
  margin-bottom: 1rem;
}

.stats-container {
  display: flex;
  justify-content: space-around;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.stats-card {
  background: #EDEDE9;
  padding: 1.25rem 1rem;
  border-radius: 10px;
  text-align: center;
  min-width: 100px;
  flex: 1 1 150px;
  margin-bottom: 0.5rem;
}

.stats-card p {
  margin: 0;
  font-size: 0.95rem;
  color: gray;
}

.stats-card span {
  font-size: 1.4rem;
  font-weight: bold;
  color: #6D8B74;
}

.chart-container {
  width: 100%;
  margin: 0 auto;
  overflow-x: auto;
}

@media (min-width: 768px) {
  .chart-container {
    width: 80%;
  }
}

@media (min-width: 992px) {
  .chart-container {
    width: 50%;
  }
}

.filters {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

input, select, button {
  padding: 0.8rem;
  border-radius: 8px;
  border: 1px solid #ccc;
  font-size: 16px;
  -webkit-appearance: none; /* Removes default styling on iOS */
}

button {
  background: #6D8B74;
  color: white;
  cursor: pointer;
  min-height: 44px; /* Better touch target */
}

button:hover {
  background: #5A7561;
}

.table-container {
  overflow-x: auto;
  width: 100%;
  margin-top: 1.5rem;
}

table {
  width: 100%;
  border-collapse: collapse;
  min-width: 600px; /* Ensures table doesn't get too narrow */
}

th, td {
  padding: 0.75rem;
  text-align: left;
}

th {
  background: #EDEDE9;
  position: sticky;
  top: 0; /* Keeps headers visible during scroll */
}

/* Mobile optimizations */
@media (max-width: 768px) {
  body {
    padding: 0.5rem;
  }
  
  .container {
    padding: 1rem 0.75rem;
    width: calc(100% - 1rem);
  }
  
  h1 {
    font-size: 1.8rem;
  }
  
  .stats-card {
    padding: 1rem 0.5rem;
    flex-basis: calc(50% - 1rem);
  }
  
  .filters {
    flex-direction: column;
    align-items: center;
  }
  
  .filters input, 
  .filters select, 
  .filters button {
    width: 100%;
    max-width: 300px;
  }
  
  th, td {
    padding: 0.6rem;
    font-size: 0.9rem;
  }
}