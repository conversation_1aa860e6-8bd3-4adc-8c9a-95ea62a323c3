import React, { useState } from 'react';
import { db } from './firebase';
import { collection, addDoc, getDocs, query, where } from 'firebase/firestore';
import './CreateAdmin.css';

const CreateAdmin = ({ currentUser }) => {
  const [displayName, setDisplayName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);

  // Only allow current admins to create other admin accounts
  if (currentUser?.role !== 'admin') {
    return (
      <div className="admin-restricted">
        <h2>Access Restricted</h2>
        <p>You need admin privileges to access this page.</p>
      </div>
    );
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setLoading(true);

    // Form validation
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      setLoading(false);
      return;
    }

    try {
      // Check if email already exists
      const usersRef = collection(db, "users");
      const emailQuery = query(usersRef, where("email", "==", email));
      const querySnapshot = await getDocs(emailQuery);
      
      if (!querySnapshot.empty) {
        setError('An account with this email already exists');
        setLoading(false);
        return;
      }

      // Create new admin user
      const newUser = {
        displayName,
        email,
        password, // Note: In production, you should NEVER store plain text passwords
        role: 'admin',
        createdAt: new Date().toISOString(),
        createdBy: currentUser.email
      };
      
      // Add to users collection
      await addDoc(collection(db, "users"), newUser);
      
      // Clear form and show success message
      setDisplayName('');
      setEmail('');
      setPassword('');
      setConfirmPassword('');
      setSuccess('Admin account created successfully!');
    } catch (error) {
      console.error('Error creating admin:', error);
      setError('Failed to create admin account');
    }

    setLoading(false);
  };

  return (
    <div className="create-admin-container">
      <div className="create-admin-form-container">
        <h1>Create Admin Account</h1>
        
        {success && <div className="success-message">{success}</div>}
        
        <form onSubmit={handleSubmit} className="create-admin-form">
          <div className="form-group">
            <label htmlFor="displayName">Display Name</label>
            <input
              type="text"
              id="displayName"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="confirmPassword">Confirm Password</label>
            <input
              type="password"
              id="confirmPassword"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />
          </div>
          
          {error && <div className="error-message">{error}</div>}
          
          <button type="submit" className="create-admin-button" disabled={loading}>
            {loading ? 'Creating...' : 'Create Admin Account'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default CreateAdmin;
