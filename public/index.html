<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="hsl(305, 46%, 51%)" />
    <meta
      name="description"
      content="A wedding guest registration form built with React and Firebase."
    />
    
    <title>Sohera_ Dashboard</title>
    
    <link rel="icon" href="%PUBLIC_URL%/logoblack.jpg" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logoblack.jpg" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;700&family=Great+Vibes&display=swap"
      rel="stylesheet"
    />

    <!-- Styles -->
    <style>
      body {
        font-family: "Cormorant Garamond", serif;
        background-color: #f9f8f6;
        color: #333;
        text-align: center;
        margin: 0;
        padding: 0;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
